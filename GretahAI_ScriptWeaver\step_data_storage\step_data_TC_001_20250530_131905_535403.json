{"test_case_id": "TC_001", "timestamp": "2025-05-30T13:19:05.535403", "data_hash": "76a9c2070cdec0d751dab13d485f14e52014d19094ad5621b8b144b03a26275f", "step_count": 4, "metadata": {"source": "initial_conversion", "conversion_method": "ai_generated", "validation_score": 6, "step_analysis": {"requires_ui_elements": true, "reason": "UI interaction 'click' detected in test steps", "actions": ["navigate", "type", "type", "click"], "locator_strategies": ["id", "id", "css"]}, "conversion_timestamp": "2025-05-30T13:19:05.535403", "test_case_objective": "Verify that a user with valid credentials can successfully log in.", "hybrid_editing_enabled": true, "has_manual_steps": true, "save_timestamp": "2025-05-30T13:19:05.535403"}, "step_data": [{"step_no": "1", "step_type": "ui", "action": "navigate", "locator_strategy": "", "locator": "", "test_data_param": "{{login_url}}", "expected_result": "login_page", "assertion_type": "url_equals", "condition": "", "timeout": 10, "step_description": "Navigate to the login page"}, {"step_no": "2", "step_type": "ui", "action": "type", "locator_strategy": "id", "locator": "userid", "test_data_param": "{{username}}", "expected_result": "userid_entered", "assertion_type": "text_equals", "condition": "", "timeout": 10, "step_description": "Enter a valid user ID"}, {"step_no": "3", "step_type": "ui", "action": "type", "locator_strategy": "id", "locator": "password", "test_data_param": "{{password}}", "expected_result": "password_entered", "assertion_type": "text_equals", "condition": "", "timeout": 10, "step_description": "Enter a valid password"}, {"step_no": "4", "step_type": "ui", "action": "click", "locator_strategy": "css", "locator": "#login-button", "test_data_param": "", "expected_result": "dashboard_page", "assertion_type": "element_visible", "condition": "", "timeout": 10, "step_description": "Click the login button"}]}